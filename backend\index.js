const express = require('express');
const multer = require('multer');
const XLSX = require('xlsx');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { sendBulkEmail, sendTestEmail } = require('./emailService');
const {
    initializeDatabase,
    createContactSet,
    insertContacts,
    createGroups,
    assignContactsToGroups,
    getAllContactSets,
    getAllContacts,
    getContactsBySet,
    getGroupsBySet,
    getContactsByGroup,
    clearAllContacts
} = require('./database');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        cb(null, Date.now() + '-' + file.originalname);
    }
});

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        // Accept only Excel files
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Only Excel files are allowed'), false);
        }
    },
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    }
});

// Helper function to parse Excel file
function parseExcelFile(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0]; // Use first sheet
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Map Excel columns to our database fields
        const contacts = jsonData.map(row => {
            return {
                principal_name: row['Principal Name'] || row['principal name'] || row['PRINCIPAL NAME'] ||
                               row.PrincipalName || row.principalName || row.PRINCIPALNAME || '',
                email: row.Email || row.email || row.EMAIL || '',
                gde_email: row.GDEemail || row.gdeemail || row.GDEEMAIL ||
                          row['GDE email'] || row['gde email'] || row['GDE EMAIL'] || '',
                cell_phone: row['Cell Phone'] || row['cell phone'] || row['CELL PHONE'] ||
                           row.CellPhone || row.cellPhone || row.CELLPHONE ||
                           row.Phone || row.phone || row.PHONE || '',
                notes: row.Notes || row.notes || row.NOTES || ''
            };
        });

        return contacts;
    } catch (error) {
        throw new Error('Failed to parse Excel file: ' + error.message);
    }
}

// Routes

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../index.html'));
});

// Upload and process Excel file
app.post('/api/upload-excel', upload.single('excelFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const filePath = req.file.path;
        const filename = req.file.originalname;

        // Parse Excel file
        const contacts = parseExcelFile(filePath);

        if (contacts.length === 0) {
            return res.status(400).json({ error: 'No valid data found in Excel file' });
        }

        // Create a new contact set
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const contactSetName = `${filename.replace(/\.[^/.]+$/, "")} - ${timestamp}`;
        const contactSet = await createContactSet(contactSetName, filename);

        // Insert contacts into database with contact set ID
        const result = await insertContacts(contacts, contactSet.id);

        // Clean up uploaded file
        fs.unlinkSync(filePath);

        res.json({
            message: 'Excel file processed successfully',
            insertedCount: result.insertedCount,
            totalRows: contacts.length,
            contactSetId: contactSet.id,
            contactSetName: contactSetName
        });

    } catch (error) {
        console.error('Error processing Excel file:', error);

        // Clean up uploaded file if it exists
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({ error: error.message });
    }
});

// Get all contacts
app.get('/api/contacts', async (req, res) => {
    try {
        const contacts = await getAllContacts();
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts:', error);
        res.status(500).json({ error: 'Failed to fetch contacts' });
    }
});

// Get all contact sets
app.get('/api/contact-sets', async (req, res) => {
    try {
        const contactSets = await getAllContactSets();
        res.json(contactSets);
    } catch (error) {
        console.error('Error fetching contact sets:', error);
        res.status(500).json({ error: 'Failed to fetch contact sets' });
    }
});

// Get contacts by contact set
app.get('/api/contact-sets/:id/contacts', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const contacts = await getContactsBySet(contactSetId);
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts by set:', error);
        res.status(500).json({ error: 'Failed to fetch contacts' });
    }
});

// Get groups by contact set
app.get('/api/contact-sets/:id/groups', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const groups = await getGroupsBySet(contactSetId);
        res.json(groups);
    } catch (error) {
        console.error('Error fetching groups by set:', error);
        res.status(500).json({ error: 'Failed to fetch groups' });
    }
});

// Get contacts by group
app.get('/api/groups/:id/contacts', async (req, res) => {
    try {
        const groupId = parseInt(req.params.id);
        const contacts = await getContactsByGroup(groupId);
        res.json(contacts);
    } catch (error) {
        console.error('Error fetching contacts by group:', error);
        res.status(500).json({ error: 'Failed to fetch contacts by group' });
    }
});

// Create groups for a contact set
app.post('/api/contact-sets/:id/create-groups', async (req, res) => {
    try {
        const contactSetId = parseInt(req.params.id);
        const { groupSize } = req.body;

        if (!groupSize || groupSize < 1) {
            return res.status(400).json({ error: 'Valid group size is required' });
        }

        // Create groups
        const groups = await createGroups(contactSetId, groupSize);

        // Assign contacts to groups
        await assignContactsToGroups(contactSetId, groupSize);

        res.json({
            message: 'Groups created successfully',
            groups: groups,
            groupCount: groups.length
        });

    } catch (error) {
        console.error('Error creating groups:', error);
        res.status(500).json({ error: 'Failed to create groups' });
    }
});

// Send bulk email to group
app.post('/api/groups/:id/send-email', upload.array('attachments', 10), async (req, res) => {
    console.log('=== EMAIL SEND REQUEST RECEIVED ===');
    console.log('Group ID:', req.params.id);
    console.log('Request body:', req.body);
    console.log('Files:', req.files);

    try {
        const groupId = parseInt(req.params.id);
        const { subject, message } = req.body;

        console.log('Parsed groupId:', groupId);
        console.log('Subject:', subject);
        console.log('Message length:', message ? message.length : 'undefined');

        if (!subject || !message) {
            console.log('Missing subject or message');
            return res.status(400).json({ error: 'Subject and message are required' });
        }

        console.log('Getting contacts for group:', groupId);
        // Get contacts for this group
        const contacts = await getContactsByGroup(groupId);
        console.log('Found contacts:', contacts.length);

        if (contacts.length === 0) {
            console.log('No contacts found in group');
            return res.status(400).json({ error: 'No contacts found in this group' });
        }

        // Filter out contacts without email addresses
        const validContacts = contacts.filter(contact => contact.email && contact.email.trim() !== '');
        console.log('Valid contacts with emails:', validContacts.length);

        if (validContacts.length === 0) {
            console.log('No valid email addresses found');
            return res.status(400).json({ error: 'No valid email addresses found in this group' });
        }

        console.log('Starting bulk email send...');
        // Send bulk email
        const results = await sendBulkEmail(validContacts, subject, message, req.files || []);
        console.log('Bulk email results:', results);

        res.json({
            message: 'Bulk email sending completed',
            total: results.total,
            successful: results.successful.length,
            failed: results.failed.length,
            results: results
        });

    } catch (error) {
        console.error('=== ERROR SENDING BULK EMAIL ===');
        console.error('Error:', error);
        console.error('Error stack:', error.stack);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            errno: error.errno,
            syscall: error.syscall,
            name: error.name
        });

        // Check for specific error types
        let errorMessage = 'Failed to send bulk email';
        let errorCode = error.code;

        if (error.code === 'EAUTH') {
            errorMessage = 'Email authentication failed. Please check your email credentials.';
        } else if (error.code === 'ECONNECTION') {
            errorMessage = 'Failed to connect to email server. Please check your email server settings.';
        } else if (error.code === 'ETIMEDOUT') {
            errorMessage = 'Email server connection timed out. Please try again.';
        } else if (error.message && error.message.includes('Invalid login')) {
            errorMessage = 'Invalid email login credentials. Please check your username and password.';
        }

        // Clean up uploaded files if there's an error
        if (req.files) {
            req.files.forEach(file => {
                if (fs.existsSync(file.path)) {
                    fs.unlinkSync(file.path);
                }
            });
        }

        res.status(500).json({
            error: errorMessage,
            details: error.message,
            code: errorCode
        });
    }
});

// Create demo contact set
app.post('/api/create-demo-data', async (req, res) => {
    try {
        // Create demo contact set
        const contactSet = await createContactSet('Demo Contact Set - Testing', 'demo_contacts.xlsx');

        // Demo contacts data
        const demoContacts = [
            { principal_name: 'John Smith', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0123456789' },
            { principal_name: 'Jane Doe', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0987654321' },
            { principal_name: 'Mike Johnson', email: '<EMAIL>', gde_email: '', cell_phone: '0111222333' },
            { principal_name: 'Sarah Wilson', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0444555666' },
            { principal_name: 'David Brown', email: '<EMAIL>', gde_email: '', cell_phone: '0777888999' },
            { principal_name: 'Lisa Davis', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0222333444' },
            { principal_name: 'Tom Anderson', email: '<EMAIL>', gde_email: '', cell_phone: '0555666777' },
            { principal_name: 'Emma Taylor', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0888999000' },
            { principal_name: 'Chris Martin', email: '<EMAIL>', gde_email: '', cell_phone: '0333444555' },
            { principal_name: 'Amy White', email: '<EMAIL>', gde_email: '<EMAIL>', cell_phone: '0666777888' }
        ];

        // Insert demo contacts
        const result = await insertContacts(demoContacts, contactSet.id);

        res.json({
            message: 'Demo contact set created successfully',
            contactSetId: contactSet.id,
            contactSetName: contactSet.name,
            insertedCount: result.insertedCount,
            totalContacts: demoContacts.length
        });

    } catch (error) {
        console.error('Error creating demo data:', error);
        res.status(500).json({ error: 'Failed to create demo data' });
    }
});

// Test email configuration endpoint
app.post('/api/test-email-config', async (req, res) => {
    try {
        console.log('Testing email configuration...');
        const result = await sendTestEmail('<EMAIL>', 'Configuration Test', 'This is a test to verify email configuration.');

        if (result.success) {
            res.json({ message: 'Email configuration is working', messageId: result.messageId });
        } else {
            res.status(500).json({ error: 'Email configuration failed', details: result.error });
        }
    } catch (error) {
        console.error('Email configuration test failed:', error);
        res.status(500).json({ error: 'Email configuration test failed', details: error.message });
    }
});

// Send test email
app.post('/api/send-test-email', async (req, res) => {
    try {
        const { to, subject, message } = req.body;

        if (!to) {
            return res.status(400).json({ error: 'Recipient email is required' });
        }

        const result = await sendTestEmail(to, subject, message);

        if (result.success) {
            res.json({ message: 'Test email sent successfully', messageId: result.messageId });
        } else {
            res.status(500).json({ error: result.error });
        }

    } catch (error) {
        console.error('Error sending test email:', error);
        res.status(500).json({ error: 'Failed to send test email' });
    }
});

// Clear all contacts
app.delete('/api/contacts', async (req, res) => {
    try {
        const result = await clearAllContacts();
        res.json({
            message: 'All contacts cleared successfully',
            deletedContacts: result.deletedContacts,
            deletedGroups: result.deletedGroups,
            deletedSets: result.deletedSets
        });
    } catch (error) {
        console.error('Error clearing contacts:', error);
        res.status(500).json({ error: 'Failed to clear contacts' });
    }
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 10MB.' });
        }
    }
    res.status(500).json({ error: error.message });
});

// Global error handlers
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    console.error('Stack:', error.stack);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Initialize database and start server
async function startServer() {
    try {
        await initializeDatabase();
        app.listen(PORT, () => {
            console.log(`Server running on http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();